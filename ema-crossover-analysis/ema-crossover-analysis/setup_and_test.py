#!/usr/bin/env python3
"""
Setup and Test Script
=====================

Automated setup and testing for the WMA Trading System.
This script helps verify the environment and run integration tests.

Author: WMAQuant Agent
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a shell command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} failed")
            if result.stderr.strip():
                print(f"   Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False

def check_uv_installation():
    """Check if uv is installed"""
    print("🔍 Checking uv installation...")
    result = subprocess.run("uv --version", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"✅ uv is installed: {result.stdout.strip()}")
        return True
    else:
        print("❌ uv is not installed")
        print("📥 Please install uv first:")
        print("   curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False

def setup_environment():
    """Setup the uv environment and install dependencies"""
    print("\n🚀 Setting up WMA Trading System Environment")
    print("=" * 50)
    
    # Check if uv is installed
    if not check_uv_installation():
        return False
    
    # Create virtual environment
    if not run_command("uv venv", "Creating virtual environment"):
        return False
    
    # Install dependencies
    if not run_command("uv pip install -e .", "Installing dependencies"):
        return False
    
    print("\n✅ Environment setup completed!")
    return True

def run_tests():
    """Run the integration tests"""
    print("\n🧪 Running Integration Tests")
    print("=" * 50)
    
    # Run the test script
    if not run_command("uv run python test_wma_integration.py", "Running integration tests"):
        return False
    
    return True

def show_usage_examples():
    """Show usage examples"""
    print("\n📚 USAGE EXAMPLES")
    print("=" * 50)
    print("1. Run WMA Analysis:")
    print("   uv run python main.py")
    print()
    print("2. Run Live Trading Agent:")
    print("   uv run python live_trading_agent.py")
    print()
    print("3. Test Supply/Demand Analysis:")
    print("   uv run python wma_supply_demand_analysis.py")
    print()
    print("4. Run Integration Tests:")
    print("   uv run python test_wma_integration.py")
    print()
    print("📊 Key Features:")
    print("   • WMA 5/10, 10/65, 5/90 crossover detection")
    print("   • Real-time supply/demand zone identification")
    print("   • Live trading during market hours (9:15 AM - 3:30 PM IST)")
    print("   • CSV logging for analysis")
    print("   • Sample data fallback when API unavailable")

def main():
    """Main setup and test function"""
    print("🎯 WMA Trading System - Setup & Test")
    print("=" * 60)
    
    # Check current directory
    current_dir = Path.cwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Check if we're in the right directory
    if not (current_dir / "main.py").exists():
        print("❌ main.py not found in current directory")
        print("📂 Please run this script from the project root directory")
        return False
    
    # Setup environment
    if not setup_environment():
        print("\n❌ Environment setup failed")
        return False
    
    # Run tests
    if not run_tests():
        print("\n❌ Tests failed")
        return False
    
    # Show usage examples
    show_usage_examples()
    
    print("\n🎉 Setup and testing completed successfully!")
    print("🚀 Your WMA Trading System is ready to use!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
