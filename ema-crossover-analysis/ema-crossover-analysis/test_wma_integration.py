#!/usr/bin/env python3
"""
Test WMA Integration
===================

Test script to verify the integrated WMA trading system works correctly.
Tests both the WMA crossover analysis and supply/demand zone identification.

Author: WMAQuant Agent
"""

import sys
import os
import traceback
from datetime import datetime

def test_wma_analyzer():
    """Test the WMA crossover analyzer"""
    print("🧪 Testing WMA Crossover Analyzer...")
    
    try:
        from main import WMACrossoverAnalyzer
        
        # Initialize analyzer with sample data
        analyzer = WMACrossoverAnalyzer()
        
        # Test with sample data
        print("📊 Running WMA analysis with sample data...")
        candle_file, signals_file = analyzer.run_analysis(
            symbol="NIFTY",
            exchange="INDEX",
            use_sample_data=True,
            days=2,
            min_separation=5,
            confirmation_periods=2
        )
        
        print(f"✅ WMA Analysis completed successfully!")
        print(f"   📁 Candle data: {candle_file}")
        print(f"   📁 Signals data: {signals_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ WMA Analyzer test failed: {e}")
        traceback.print_exc()
        return False

def test_supply_demand_analysis():
    """Test the supply/demand analysis with DataFrame"""
    print("\n🧪 Testing Supply/Demand Analysis...")
    
    try:
        from wma_supply_demand_analysis import analyze_wma_supply_demand_df
        import pandas as pd
        
        # Check if we have candle data from previous test
        candle_file = "output/candle_data.csv"
        if os.path.exists(candle_file):
            print(f"📖 Reading candle data from {candle_file}")
            df = pd.read_csv(candle_file)
            
            # Test DataFrame version
            result_df = analyze_wma_supply_demand_df(df)
            
            print(f"✅ Supply/Demand analysis completed!")
            print(f"   📊 Total records: {len(result_df)}")
            
            # Check if Price Action column was added
            if 'Price Action' in result_df.columns:
                supply_count = len(result_df[result_df['Price Action'] == 'Supply'])
                demand_count = len(result_df[result_df['Price Action'] == 'Demand'])
                print(f"   🔴 Supply zones: {supply_count}")
                print(f"   🟢 Demand zones: {demand_count}")
            
            return True
        else:
            print(f"⚠️  Candle data file not found: {candle_file}")
            return False
            
    except Exception as e:
        print(f"❌ Supply/Demand analysis test failed: {e}")
        traceback.print_exc()
        return False

def test_live_trading_agent():
    """Test the live trading agent initialization"""
    print("\n🧪 Testing Live Trading Agent...")
    
    try:
        from live_trading_agent import LiveTradingAgent
        
        # Initialize agent
        agent = LiveTradingAgent()
        
        # Test market hours check
        is_open = agent.is_market_open()
        time_to_open = agent.get_time_to_market_open()
        
        print(f"✅ Live Trading Agent initialized successfully!")
        print(f"   🕐 Market open: {is_open}")
        print(f"   ⏰ Time to market open: {time_to_open}")
        
        # Test data fetching (sample data)
        print("📡 Testing data fetch and update...")
        success = agent.fetch_and_update_data(
            symbol="NIFTY",
            exchange="INDEX", 
            use_sample_data=True,
            days=2
        )
        
        if success:
            signals = agent.get_latest_signals()
            print(f"   📈 Latest signals retrieved: {signals['total_candles']} candles")
            print(f"   🎯 Supply zones: {signals['supply_zones']}")
            print(f"   🎯 Demand zones: {signals['demand_zones']}")
        
        return success
        
    except Exception as e:
        print(f"❌ Live Trading Agent test failed: {e}")
        traceback.print_exc()
        return False

def test_config():
    """Test configuration loading"""
    print("\n🧪 Testing Configuration...")
    
    try:
        from config import get_config, validate_config, print_config
        
        config = get_config()
        is_valid, error_msg = validate_config()
        
        print(f"✅ Configuration loaded successfully!")
        print(f"   ✔️ Valid: {is_valid}")
        if not is_valid:
            print(f"   ⚠️ Error: {error_msg}")
        
        print("\n📋 Current Configuration:")
        print_config()
        
        return is_valid
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def run_integration_test():
    """Run complete integration test"""
    print("🚀 WMA Trading System Integration Test")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Track test results
    tests = []
    
    # Test 1: Configuration
    tests.append(("Configuration", test_config()))
    
    # Test 2: WMA Analyzer
    tests.append(("WMA Analyzer", test_wma_analyzer()))
    
    # Test 3: Supply/Demand Analysis
    tests.append(("Supply/Demand Analysis", test_supply_demand_analysis()))
    
    # Test 4: Live Trading Agent
    tests.append(("Live Trading Agent", test_live_trading_agent()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"📈 Tests passed: {passed}/{total}")
    print(f"📊 Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! WMA Trading System is ready!")
        print("\n🚀 You can now run:")
        print("   • python main.py                    # WMA analysis")
        print("   • python live_trading_agent.py      # Live trading")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Please check the errors above.")
    
    print("=" * 60)
    return passed == total

if __name__ == "__main__":
    try:
        success = run_integration_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {e}")
        traceback.print_exc()
        sys.exit(1)
