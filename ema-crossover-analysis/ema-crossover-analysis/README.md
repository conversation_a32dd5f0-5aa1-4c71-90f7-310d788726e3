# WMA Crossover Trading System

🎯 **Real-time WMA-based algorithmic trading system for Indian stock market**

A comprehensive WMA (Weighted Moving Average) crossover analysis system with real-time supply/demand zone identification, specifically designed for Indian stock market trading during market hours (9:15 AM - 3:30 PM IST).

## 🚀 Key Features

### WMA Crossover Analysis
- **WMA 5/10** - Primary crossovers for supply/demand zone identification
- **WMA 10/65** - Medium-term trend analysis
- **WMA 5/90** - Long-term trend analysis

### Real-time Trading Engine
- Live 1-minute OHLCV data fetching via DhanHQ APIs
- Continuous WMA indicator calculation and crossover detection
- Dynamic supply/demand zone identification
- Market hours validation (9:15 AM - 3:30 PM IST)
- Automatic fallback to sample data when API unavailable

### Supply/Demand Zone Detection
- **Supply Zones**: Highest high between BUY→SELL crossovers
- **Demand Zones**: Lowest low between SELL→BUY crossovers
- Real-time zone updates with each new candle

### Data Management
- In-memory processing (no intermediate CSV files)
- Optional CSV logging for analysis
- Configurable data retention periods
- Clean, structured output formats

## 🛠️ Quick Setup with UV

### Prerequisites
Install `uv` (fast Python package manager):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Automated Setup & Test
```bash
# Navigate to project directory
cd ema-crossover-analysis

# Run automated setup and testing
python setup_and_test.py
```

### Manual Setup
```bash
# Create virtual environment
uv venv

# Install dependencies
uv pip install -e .

# Run integration tests
uv run python test_wma_integration.py
```

## 🎯 Usage Examples

### 1. WMA Analysis (Batch Mode)
```bash
uv run python main.py
```

This will:
1. Fetch 2 days of 1-minute candle data for NIFTY
2. Calculate WMA 5, 10, 65, 90 indicators
3. Detect WMA crossovers with noise reduction
4. Identify supply/demand zones
5. Save results to CSV files in `output/` directory

### 2. Live Trading Agent (Real-time Mode)
```bash
uv run python live_trading_agent.py
```

Features:
- Runs continuously during market hours (9:15 AM - 3:30 PM IST)
- Updates WMA indicators every minute
- Real-time supply/demand zone identification
- Console logging of latest signals and zones
- Optional CSV logging for analysis

### 3. Supply/Demand Analysis (Standalone)
```bash
uv run python wma_supply_demand_analysis.py
```

### 4. Integration Testing
```bash
uv run python test_wma_integration.py
```

## ⚙️ Configuration

### Dhan API Setup (for Live Data)

1. **Get Dhan API Credentials**:
   - Login to Dhan web platform
   - Go to API section
   - Generate API credentials (Client Code & Token ID)

2. **Configure Credentials**:
   Edit `config.py`:
   ```python
   DHAN_CLIENT_CODE = "your_client_code"
   DHAN_TOKEN_ID = "your_token_id"
   USE_SAMPLE_DATA = False  # Set to True for testing
   ```

3. **Test Configuration**:
   ```bash
   uv run python test_wma_integration.py
   ```

### Trading Parameters
```python
# Symbol Configuration
SYMBOL = "NIFTY"        # NIFTY, BANKNIFTY, RELIANCE, etc.
EXCHANGE = "INDEX"      # INDEX, NSE, BSE

# Analysis Parameters
TRADING_DAYS = 2        # Days of data to analyze
MIN_SEPARATION_MINUTES = 10  # Min time between signals
CONFIRMATION_PERIODS = 2     # Crossover confirmation periods

# Live Trading
LIVE_UPDATE_INTERVAL_MINUTES = 1  # Update frequency
ENABLE_LIVE_LOGGING = True        # CSV logging during live trading
```

## 📊 Output Files

The system generates comprehensive CSV files in the `output/` directory:

### 1. `candle_data.csv` (WMA Analysis)
Enhanced candle data with WMA indicators:
- **OHLCV Data**: timestamp, open, high, low, close, volume
- **WMA Indicators**: wma_5, wma_10, wma_65, wma_90
- **Crossover Signals**: all_wma_5_10_crossovers, all_wma_10_65_crossovers, all_wma_5_90_crossovers
- **Signal Filters**: wma_5_10_filtered_signal, wma_10_65_filtered_signal, wma_5_90_filtered_signal

### 2. `signals.csv` (All WMA Signals)
Consolidated WMA crossover signals:
- `timestamp` - When the signal occurred
- `indicator_type` - WMA_5_10, WMA_10_65, or WMA_5_90
- `signal` - BUY or SELL
- `price` - Price at signal time
- `indicator_1_value` - First WMA value
- `indicator_2_value` - Second WMA value

### 3. `live_candle_data_with_zones.csv` (Live Trading)
Real-time data with supply/demand zones:
- All candle data and WMA indicators
- **Price Action** column with 'Supply', 'Demand', or empty
- Real-time zone identification based on WMA 5/10 crossovers

### 4. `candle_data_with_price_action.csv` (Supply/Demand Analysis)
Post-processed data with identified zones:
- Complete candle and indicator data
- Supply zones (highest high between BUY→SELL)
- Demand zones (lowest low between SELL→BUY)

## 🏗️ Project Structure

```
ema-crossover-analysis/
├── main.py                        # WMA crossover analyzer (refactored)
├── wma_supply_demand_analysis.py  # Supply/demand zone identification
├── live_trading_agent.py          # Real-time trading engine
├── config.py                      # Configuration management
├── test_wma_integration.py        # Integration tests
├── setup_and_test.py             # Automated setup script
├── pyproject.toml                 # Project dependencies
├── uv.lock                       # Dependency lock file
├── README.md                     # This documentation
└── output/                       # Generated analysis files
    ├── candle_data.csv
    ├── signals.csv
    ├── live_candle_data_with_zones.csv
    └── candle_data_with_price_action.csv
```

## 📈 Example Output

### WMA Analysis
```
🚀 Starting WMA Crossover Analysis
==================================================
📊 Generating sample data for NIFTY (2 days + 10 warmup)
✓ Generated 4500 total data points
📈 Calculating WMA indicators with proper warmup initialization
✓ WMA indicators calculated using 3750 warmup points
📊 Analysis data: 750 points with properly initialized WMA indicators
🔍 Detecting WMA crossovers with improved accuracy
✓ WMA crossover detection completed:
  📊 WMA 5/10: 12 BUY, 11 SELL (Raw: 15/14)
  📉 WMA 10/65: 3 BUY, 4 SELL (Raw: 4/5)
  🎯 WMA 5/90: 2 BUY, 1 SELL (Raw: 2/2)
💾 Enhanced candle data with WMA indicators saved
📈 Total Supply zones identified: 8
📉 Total Demand zones identified: 7
✅ WMA Analysis completed successfully!
```

### Live Trading Agent
```
🚀 Starting Live WMA Trading Agent
============================================================
📊 Monitoring: NIFTY on INDEX
🔄 Update interval: 1 minute(s)
💾 CSV logging: Enabled

🟢 Market is OPEN - 2024-12-21 10:30:15
📡 Fetching latest data for NIFTY...
✅ Data updated successfully at 10:30:15
📈 Latest signals: WMA5/10: 2, WMA10/65: 1, WMA5/90: 0
🎯 Supply/Demand zones: 3 Supply, 2 Demand
💰 Current: 24156.75 | WMA5: 24158.23 | WMA10: 24152.45 | WMA65: 24145.67
⏰ Next update in 1 minute(s)...
```

## 🔧 Dependencies

- `pandas>=2.2.3` - Data manipulation and analysis
- `numpy>=2.2.6` - Numerical computing
- `dhan-tradehull>=3.0.6` - Dhan API integration
- `setuptools>=80.9.0` - Package management

## 📝 Notes

- **Market Hours**: System respects Indian market hours (9:15 AM - 3:30 PM IST)
- **Weekends**: Automatically excluded from analysis and live trading
- **Data Warmup**: Uses 90+ historical candles for proper WMA initialization
- **Fallback**: Automatically uses sample data if API unavailable
- **Memory Efficient**: In-memory processing with optional CSV logging

## 🐛 Troubleshooting

1. **Import Errors**: Run `uv pip install -e .` to install dependencies
2. **API Issues**: Check Dhan credentials and network connectivity
3. **No Signals**: Increase data period or adjust crossover parameters
4. **Market Closed**: Live agent waits automatically for market opening
5. **Permission Errors**: Ensure write access to `output/` directory

## 🚀 Quick Start Commands

```bash
# Setup and test everything
python setup_and_test.py

# Run WMA analysis
uv run python main.py

# Start live trading
uv run python live_trading_agent.py

# Test integration
uv run python test_wma_integration.py
```

---

**🎯 Ready to trade with WMA crossovers and supply/demand zones!**