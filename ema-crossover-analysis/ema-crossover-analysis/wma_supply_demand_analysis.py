#!/usr/bin/env python3
"""
WMA 5x10 Supply and Demand Analysis
This script analyzes candle data to identify supply and demand zones based on WMA 5x10 crossovers.
"""

import pandas as pd
import numpy as np
from datetime import datetime

def analyze_wma_supply_demand_df(df: pd.DataFrame) -> pd.DataFrame:
    """
    Analyze the candle data to identify supply and demand zones based on WMA 5x10 crossovers.

    Supply: Between BUY and SELL signals, find the candle with highest High value
    Demand: Between SELL and BUY signals, find the candle with lowest Low value

    Args:
        df: DataFrame with candle data and WMA crossover signals

    Returns:
        DataFrame with 'Price Action' column added
    """

    print(f"Analyzing {len(df)} candle data points for supply/demand zones...")
    df = df.copy()  # Work with a copy to avoid modifying original
    
    # Initialize Price Action column with empty values
    df.insert(1, 'Price Action', '')
    
    # Find WMA 5x10 crossover signals - looking for actual BUY/SELL signals
    buy_signals = df[df['all_wma_5_10_crossovers'] == 'BUY'].index.tolist()
    sell_signals = df[df['all_wma_5_10_crossovers'] == 'SELL'].index.tolist()
    
    print(f"Found {len(buy_signals)} BUY signals and {len(sell_signals)} SELL signals")
    
    if len(buy_signals) == 0 and len(sell_signals) == 0:
        print("No BUY/SELL signals found. Let me check the data structure...")
        print("Unique values in 'all_wma_5_10_crossovers':")
        print(df['all_wma_5_10_crossovers'].unique())
        print("\nUnique values in 'wma_5_10_crossover_type':")
        print(df['wma_5_10_crossover_type'].unique())
        return df
    
    # Combine and sort all signals with their types
    all_signals = []
    for idx in buy_signals:
        all_signals.append((idx, 'BUY'))
    for idx in sell_signals:
        all_signals.append((idx, 'SELL'))
    
    # Sort by index
    all_signals.sort(key=lambda x: x[0])
    
    print(f"Processing {len(all_signals)} total signals...")
    print("Signal sequence:")
    for i, (idx, signal_type) in enumerate(all_signals):
        timestamp = df.at[idx, 'timestamp']
        print(f"{i+1}. {signal_type} at index {idx} ({timestamp})")
    
    # Process signals in sequence
    for i in range(len(all_signals) - 1):
        current_signal_idx, current_signal_type = all_signals[i]
        next_signal_idx, next_signal_type = all_signals[i + 1]
        
        # Define the range between current and next signal (inclusive)
        start_idx = current_signal_idx
        end_idx = next_signal_idx
        
        if current_signal_type == 'BUY' and next_signal_type == 'SELL':
            # Between BUY and SELL: Find highest High (Supply)
            segment = df.iloc[start_idx:end_idx + 1]
            max_high_idx = segment['high'].idxmax()
            max_high_value = df.at[max_high_idx, 'high']
            df.at[max_high_idx, 'Price Action'] = 'Supply'
            print(f"Supply found at index {max_high_idx} (High: {max_high_value}) between BUY at {start_idx} and SELL at {end_idx}")
            
        elif current_signal_type == 'SELL' and next_signal_type == 'BUY':
            # Between SELL and BUY: Find lowest Low (Demand)
            segment = df.iloc[start_idx:end_idx + 1]
            min_low_idx = segment['low'].idxmin()
            min_low_value = df.at[min_low_idx, 'low']
            df.at[min_low_idx, 'Price Action'] = 'Demand'
            print(f"Demand found at index {min_low_idx} (Low: {min_low_value}) between SELL at {start_idx} and BUY at {end_idx}")
    
    # Handle the case after the last signal if needed
    if all_signals:
        last_signal_idx, last_signal_type = all_signals[-1]
        if last_signal_type == 'BUY':
            # If last signal is BUY, check if there are any remaining candles to find Supply
            remaining_segment = df.iloc[last_signal_idx:]
            if len(remaining_segment) > 1:
                max_high_idx = remaining_segment['high'].idxmax()
                max_high_value = df.at[max_high_idx, 'high']
                df.at[max_high_idx, 'Price Action'] = 'Supply'
                print(f"Supply found at index {max_high_idx} (High: {max_high_value}) after final BUY at {last_signal_idx}")
        elif last_signal_type == 'SELL':
            # If last signal is SELL, check if there are any remaining candles to find Demand
            remaining_segment = df.iloc[last_signal_idx:]
            if len(remaining_segment) > 1:
                min_low_idx = remaining_segment['low'].idxmin()
                min_low_value = df.at[min_low_idx, 'low']
                df.at[min_low_idx, 'Price Action'] = 'Demand'
                print(f"Demand found at index {min_low_idx} (Low: {min_low_value}) after final SELL at {last_signal_idx}")
    
    # Count supply and demand zones
    supply_count = len(df[df['Price Action'] == 'Supply'])
    demand_count = len(df[df['Price Action'] == 'Demand'])
    
    print(f"\n✅ Supply/Demand analysis complete!")
    print(f"📈 Total Supply zones identified: {supply_count}")
    print(f"📉 Total Demand zones identified: {demand_count}")

    # Display some sample supply and demand zones
    if supply_count > 0:
        print("\n🔴 Sample Supply zones:")
        supply_zones = df[df['Price Action'] == 'Supply'][['timestamp', 'high', 'low', 'close', 'Price Action']].head(3)
        print(supply_zones.to_string(index=False))

    if demand_count > 0:
        print("\n🟢 Sample Demand zones:")
        demand_zones = df[df['Price Action'] == 'Demand'][['timestamp', 'high', 'low', 'close', 'Price Action']].head(3)
        print(demand_zones.to_string(index=False))

    return df


def analyze_wma_supply_demand(csv_file_path):
    """
    Legacy function for backward compatibility - reads CSV and calls the DataFrame version

    Args:
        csv_file_path: Path to CSV file with candle data

    Returns:
        DataFrame with 'Price Action' column added
    """
    print(f"Reading data from {csv_file_path}...")
    df = pd.read_csv(csv_file_path)

    # Call the DataFrame version
    result_df = analyze_wma_supply_demand_df(df)

    # Save the updated CSV for backward compatibility
    output_file = csv_file_path.replace('.csv', '_with_price_action.csv')
    result_df.to_csv(output_file, index=False)
    print(f"Updated data saved to: {output_file}")

    return result_df

if __name__ == "__main__":
    csv_file_path = "./output/candle_data.csv"
    
    try:
        result_df = analyze_wma_supply_demand(csv_file_path)
        print("\nWMA 5x10 Supply and Demand analysis completed successfully!")
        
    except FileNotFoundError:
        print(f"Error: File {csv_file_path} not found!")
    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()
