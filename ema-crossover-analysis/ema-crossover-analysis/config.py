"""
Configuration file for EMA Crossover Analysis
============================================

Update your Dhan API credentials here to fetch real market data.
"""

# Dhan API Credentials
# ====================
# Get these from your Dhan account:
# 1. Login to Dhan web platform
# 2. Go to API section
# 3. Generate API credentials

DHAN_CLIENT_CODE = "**********"  # Replace with your Dhan client code (string)
DHAN_TOKEN_ID = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"     # Replace with your Dhan token ID (string)

# Example (uncomment and update with your actual credentials):
# DHAN_CLIENT_CODE = "**********"
# DHAN_TOKEN_ID = "your_token_id_here"

# Trading Configuration
# ====================

# Symbol to analyze
SYMBOL = "NIFTY"  # Options: "NIFTY", "BANKNIFTY", "SENSEX", etc.

# Exchange
EXCHANGE = "INDEX"  # Options: "INDEX", "NSE", "BSE"

# Analysis Parameters
# ==================

# Number of trading days to fetch and analyze
TRADING_DAYS = 2

# Use sample data instead of real API data (for testing)
USE_SAMPLE_DATA = False  # Set to True for testing without API

# EMA Crossover Parameters
# =======================

# Minimum minutes between signals (to avoid noise)
MIN_SEPARATION_MINUTES = 10

# Number of periods to confirm crossover (reduces false signals)
CONFIRMATION_PERIODS = 2

# Market Hours (IST)
# =================
MARKET_START_HOUR = 9
MARKET_START_MINUTE = 15
MARKET_END_HOUR = 15
MARKET_END_MINUTE = 30

# Output Configuration
# ===================
OUTPUT_DIR = "output"
CANDLE_DATA_FILE = "candle_data.csv"
SIGNALS_FILE = "signals.csv"

# Validation Settings
# ==================
ENABLE_SIGNAL_VALIDATION = True
SHOW_DETAILED_VALIDATION = True

# Live Trading Settings
# ====================
LIVE_UPDATE_INTERVAL_MINUTES = 1  # Minutes between data updates
ENABLE_LIVE_LOGGING = True  # Enable CSV logging during live trading
LIVE_DATA_RETENTION_DAYS = 2  # Days of data to keep in memory

def get_config():
    """
    Get configuration dictionary
    
    Returns:
        dict: Configuration parameters
    """
    return {
        'dhan_client_code': DHAN_CLIENT_CODE,
        'dhan_token_id': DHAN_TOKEN_ID,
        'symbol': SYMBOL,
        'exchange': EXCHANGE,
        'trading_days': TRADING_DAYS,
        'use_sample_data': USE_SAMPLE_DATA,
        'min_separation_minutes': MIN_SEPARATION_MINUTES,
        'confirmation_periods': CONFIRMATION_PERIODS,
        'market_start_hour': MARKET_START_HOUR,
        'market_start_minute': MARKET_START_MINUTE,
        'market_end_hour': MARKET_END_HOUR,
        'market_end_minute': MARKET_END_MINUTE,
        'output_dir': OUTPUT_DIR,
        'candle_data_file': CANDLE_DATA_FILE,
        'signals_file': SIGNALS_FILE,
        'enable_signal_validation': ENABLE_SIGNAL_VALIDATION,
        'show_detailed_validation': SHOW_DETAILED_VALIDATION,
        'live_update_interval_minutes': LIVE_UPDATE_INTERVAL_MINUTES,
        'enable_live_logging': ENABLE_LIVE_LOGGING,
        'live_data_retention_days': LIVE_DATA_RETENTION_DAYS
    }

def validate_config():
    """
    Validate configuration parameters
    
    Returns:
        tuple: (is_valid, error_message)
    """
    config = get_config()
    
    # Check if API credentials are provided when not using sample data
    if not config['use_sample_data']:
        if not config['dhan_client_code'] or not config['dhan_token_id']:
            return False, "Dhan API credentials are required when USE_SAMPLE_DATA is False"
    
    # Validate trading days
    if config['trading_days'] < 1 or config['trading_days'] > 30:
        return False, "TRADING_DAYS must be between 1 and 30"
    
    # Validate separation minutes
    if config['min_separation_minutes'] < 1:
        return False, "MIN_SEPARATION_MINUTES must be at least 1"
    
    # Validate confirmation periods
    if config['confirmation_periods'] < 1:
        return False, "CONFIRMATION_PERIODS must be at least 1"
    
    return True, "Configuration is valid"

def print_config():
    """Print current configuration"""
    config = get_config()
    
    print("📋 Current Configuration:")
    print("=" * 40)
    print(f"Symbol: {config['symbol']}")
    print(f"Exchange: {config['exchange']}")
    print(f"Trading Days: {config['trading_days']}")
    print(f"Use Sample Data: {config['use_sample_data']}")
    print(f"Min Separation: {config['min_separation_minutes']} minutes")
    print(f"Confirmation Periods: {config['confirmation_periods']}")
    
    if config['use_sample_data']:
        print("🧪 Using sample data for analysis")
    else:
        if config['dhan_client_code'] and config['dhan_token_id']:
            print("🔌 Using real Dhan API data")
        else:
            print("⚠️  Dhan credentials not configured - will fall back to sample data")
    
    print()
