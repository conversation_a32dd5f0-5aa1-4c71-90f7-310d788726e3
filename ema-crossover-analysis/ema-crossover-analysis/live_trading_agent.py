#!/usr/bin/env python3
"""
Live WMA Trading Agent
======================

Real-time trading engine focused on WMA crossovers for Indian stock market.
Integrates WMA crossover analysis with supply/demand zone identification.

Features:
- Live 1-minute OHLCV data fetching via DhanHQ APIs
- Real-time WMA 5/10, WMA 10/65, WMA 5/90 crossover detection
- Dynamic supply/demand zone identification
- Market hours validation (9:15 AM - 3:30 PM IST)
- Optional CSV logging for analysis

Author: WMAQuant Agent - Algorithmic Trading Specialist
"""

import pandas as pd
import numpy as np
import datetime
import time
import os
from typing import Optional, Dict, Any
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Import our modules
from main import WMACrossoverAnalyzer
from wma_supply_demand_analysis import analyze_wma_supply_demand_df
from config import get_config, validate_config


class LiveTradingAgent:
    """
    Live WMA Trading Agent for real-time crossover analysis and supply/demand identification
    """
    
    def __init__(self, client_code: Optional[str] = None, token_id: Optional[str] = None):
        """
        Initialize the Live Trading Agent
        
        Args:
            client_code: Dhan client code (optional)
            token_id: Dhan token ID (optional)
        """
        self.client_code = client_code
        self.token_id = token_id
        
        # Initialize WMA analyzer
        self.analyzer = WMACrossoverAnalyzer(client_code, token_id)
        
        # Market hours (IST)
        self.market_start_hour = 9
        self.market_start_minute = 15
        self.market_end_hour = 15
        self.market_end_minute = 30
        
        # Data storage
        self.live_data = pd.DataFrame()
        self.last_update_time = None
        
        print("🤖 Live WMA Trading Agent initialized")
    
    def is_market_open(self) -> bool:
        """
        Check if Indian stock market is currently open
        
        Returns:
            bool: True if market is open, False otherwise
        """
        now = datetime.datetime.now()
        
        # Check if it's a weekday (Monday=0, Sunday=6)
        if now.weekday() >= 5:  # Saturday or Sunday
            return False
        
        # Check market hours (9:15 AM to 3:30 PM IST)
        market_start = now.replace(hour=self.market_start_hour, minute=self.market_start_minute, second=0, microsecond=0)
        market_end = now.replace(hour=self.market_end_hour, minute=self.market_end_minute, second=0, microsecond=0)
        
        return market_start <= now <= market_end
    
    def get_time_to_market_open(self) -> str:
        """
        Get time remaining until market opens
        
        Returns:
            str: Formatted time until market opens
        """
        now = datetime.datetime.now()
        
        # If it's weekend, calculate time to next Monday
        if now.weekday() >= 5:
            days_until_monday = 7 - now.weekday()
            next_monday = now + datetime.timedelta(days=days_until_monday)
            market_open = next_monday.replace(hour=self.market_start_hour, minute=self.market_start_minute, second=0, microsecond=0)
        else:
            # Same day or next day
            market_open = now.replace(hour=self.market_start_hour, minute=self.market_start_minute, second=0, microsecond=0)
            if now > market_open:
                # Market already closed today, next opening is tomorrow
                market_open += datetime.timedelta(days=1)
        
        time_diff = market_open - now
        hours, remainder = divmod(time_diff.total_seconds(), 3600)
        minutes, _ = divmod(remainder, 60)
        
        return f"{int(hours)}h {int(minutes)}m"
    
    def fetch_and_update_data(self, symbol: str = "NIFTY", exchange: str = "INDEX", 
                             use_sample_data: bool = False, days: int = 2) -> bool:
        """
        Fetch latest candle data and update indicators
        
        Args:
            symbol: Trading symbol
            exchange: Exchange name
            use_sample_data: Whether to use sample data
            days: Number of days of data to maintain
            
        Returns:
            bool: True if data was successfully updated
        """
        try:
            print(f"📡 Fetching latest data for {symbol}...")
            
            # Get fresh data
            if use_sample_data:
                data = self.analyzer.generate_sample_data(symbol, days)
            else:
                data = self.analyzer.fetch_live_data(symbol, exchange, days)
                if data is None:
                    print("⚠️  Failed to fetch live data, using sample data")
                    data = self.analyzer.generate_sample_data(symbol, days)
            
            # Calculate indicators
            data_with_indicators = self.analyzer.calculate_indicators(data)
            
            # Detect crossovers
            data_with_signals = self.analyzer.detect_crossovers(data_with_indicators)
            
            # Analyze supply/demand zones
            data_with_zones = analyze_wma_supply_demand_df(data_with_signals)
            
            # Update live data
            self.live_data = data_with_zones
            self.last_update_time = datetime.datetime.now()
            
            print(f"✅ Data updated successfully at {self.last_update_time.strftime('%H:%M:%S')}")
            return True
            
        except Exception as e:
            print(f"❌ Error updating data: {e}")
            return False
    
    def get_latest_signals(self, limit: int = 5) -> Dict[str, Any]:
        """
        Get latest WMA crossover signals and supply/demand zones
        
        Args:
            limit: Number of recent signals to return
            
        Returns:
            dict: Latest signals and zones information
        """
        if self.live_data.empty:
            return {"error": "No data available"}
        
        # Get recent WMA signals
        wma_5_10_signals = self.live_data[self.live_data['wma_5_10_signal'].isin(['BUY', 'SELL'])].tail(limit)
        wma_10_65_signals = self.live_data[self.live_data['wma_10_65_signal'].isin(['BUY', 'SELL'])].tail(limit)
        wma_5_90_signals = self.live_data[self.live_data['wma_5_90_signal'].isin(['BUY', 'SELL'])].tail(limit)
        
        # Get supply/demand zones
        supply_zones = self.live_data[self.live_data['Price Action'] == 'Supply'].tail(limit)
        demand_zones = self.live_data[self.live_data['Price Action'] == 'Demand'].tail(limit)
        
        # Get latest candle info
        latest_candle = self.live_data.iloc[-1] if not self.live_data.empty else None
        
        return {
            "timestamp": self.last_update_time.isoformat() if self.last_update_time else None,
            "latest_candle": {
                "timestamp": latest_candle['timestamp'] if latest_candle is not None else None,
                "close": float(latest_candle['close']) if latest_candle is not None else None,
                "wma_5": float(latest_candle['wma_5']) if latest_candle is not None else None,
                "wma_10": float(latest_candle['wma_10']) if latest_candle is not None else None,
                "wma_65": float(latest_candle['wma_65']) if latest_candle is not None else None,
                "wma_90": float(latest_candle['wma_90']) if latest_candle is not None else None,
            } if latest_candle is not None else None,
            "wma_5_10_signals": len(wma_5_10_signals),
            "wma_10_65_signals": len(wma_10_65_signals),
            "wma_5_90_signals": len(wma_5_90_signals),
            "supply_zones": len(supply_zones),
            "demand_zones": len(demand_zones),
            "total_candles": len(self.live_data)
        }
    
    def log_to_csv(self, filename: str = "live_candle_data_with_zones.csv") -> str:
        """
        Save current live data to CSV file
        
        Args:
            filename: Output filename
            
        Returns:
            str: Path to saved file
        """
        if self.live_data.empty:
            print("⚠️  No data to save")
            return ""
        
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        filepath = os.path.join(output_dir, filename)
        
        self.live_data.to_csv(filepath, index=False)
        
        print(f"💾 Live data saved to: {filepath}")
        print(f"   📊 Total records: {len(self.live_data)}")
        print(f"   🕐 Last update: {self.last_update_time}")
        
        return filepath


def run_live_trading(symbol: str = "NIFTY", exchange: str = "INDEX", 
                    client_code: Optional[str] = None, token_id: Optional[str] = None,
                    use_sample_data: bool = False, days: int = 2, 
                    update_interval_minutes: int = 1, enable_logging: bool = True) -> None:
    """
    Run live trading agent with continuous monitoring
    
    Args:
        symbol: Trading symbol to monitor
        exchange: Exchange name
        client_code: Dhan client code
        token_id: Dhan token ID
        use_sample_data: Whether to use sample data
        days: Number of days of data to maintain
        update_interval_minutes: Minutes between data updates
        enable_logging: Whether to log data to CSV
    """
    print("🚀 Starting Live WMA Trading Agent")
    print("=" * 60)
    
    # Initialize agent
    agent = LiveTradingAgent(client_code, token_id)
    
    print(f"📊 Monitoring: {symbol} on {exchange}")
    print(f"🔄 Update interval: {update_interval_minutes} minute(s)")
    print(f"💾 CSV logging: {'Enabled' if enable_logging else 'Disabled'}")
    print("=" * 60)
    
    try:
        while True:
            current_time = datetime.datetime.now()
            
            if agent.is_market_open():
                print(f"\n🟢 Market is OPEN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Update data
                success = agent.fetch_and_update_data(symbol, exchange, use_sample_data, days)
                
                if success:
                    # Get and display latest signals
                    signals = agent.get_latest_signals()
                    print(f"📈 Latest signals: WMA5/10: {signals['wma_5_10_signals']}, "
                          f"WMA10/65: {signals['wma_10_65_signals']}, WMA5/90: {signals['wma_5_90_signals']}")
                    print(f"🎯 Supply/Demand zones: {signals['supply_zones']} Supply, {signals['demand_zones']} Demand")
                    
                    if signals['latest_candle']:
                        candle = signals['latest_candle']
                        print(f"💰 Current: {candle['close']:.2f} | WMA5: {candle['wma_5']:.2f} | "
                              f"WMA10: {candle['wma_10']:.2f} | WMA65: {candle['wma_65']:.2f}")
                    
                    # Log to CSV if enabled
                    if enable_logging:
                        agent.log_to_csv()
                
                # Wait for next update
                print(f"⏰ Next update in {update_interval_minutes} minute(s)...")
                time.sleep(update_interval_minutes * 60)
                
            else:
                time_to_open = agent.get_time_to_market_open()
                print(f"\n🔴 Market is CLOSED - Next opening in {time_to_open}")
                print(f"⏰ Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Wait 5 minutes before checking again
                time.sleep(300)
    
    except KeyboardInterrupt:
        print("\n\n🛑 Live trading agent stopped by user")
        if enable_logging and not agent.live_data.empty:
            final_file = agent.log_to_csv("final_live_data_with_zones.csv")
            print(f"📁 Final data saved to: {final_file}")
    
    except Exception as e:
        print(f"\n❌ Error in live trading agent: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Load configuration
    try:
        config = get_config()
        is_valid, error_msg = validate_config()
        
        if not is_valid:
            print(f"❌ Configuration error: {error_msg}")
            exit(1)
        
        # Run live trading with config
        run_live_trading(
            symbol=config['symbol'],
            exchange=config['exchange'],
            client_code=config['dhan_client_code'],
            token_id=config['dhan_token_id'],
            use_sample_data=config['use_sample_data'],
            days=config['trading_days'],
            update_interval_minutes=1,
            enable_logging=True
        )
        
    except ImportError:
        print("⚠️  Config not available, using defaults")
        run_live_trading(
            symbol="NIFTY",
            exchange="INDEX",
            use_sample_data=True,
            days=2,
            enable_logging=True
        )
