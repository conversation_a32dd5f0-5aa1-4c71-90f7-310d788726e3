"""
WMA Crossover Analysis for Minute-based Candle Data
===================================================

This script analyzes 1-minute candle data for WMA crossovers:
- WMA 5/10 crossovers for supply/demand zone identification
- WMA 10/65 crossovers for medium-term trend analysis
- WMA 5/90 crossovers for long-term trend analysis

Outputs:
1. candle_data.csv - All 1-minute candle close price data with WMA indicators
2. signals.csv - WMA crossover signals with timestamps

Author: WMAQuant Agent - Algorithmic Trading Specialist
"""

import pandas as pd
import numpy as np
import datetime
import os
from typing import Tuple, Optional
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Import configuration
try:
    from config import get_config, validate_config, print_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    print("⚠️  config.py not found, using default configuration")

try:
    from Dhan_Tradehull import Tradehull
    DHAN_AVAILABLE = True
except ImportError:
    DHAN_AVAILABLE = False
    print("Warning: Dhan_Tradehull not available. Using sample data generation.")


class WMACrossoverAnalyzer:
    """
    WMA Crossover Analysis class for processing minute-based candle data
    Focuses on WMA 5/10, WMA 10/65, and WMA 5/90 crossovers for trading signals
    """

    def __init__(self, client_code: Optional[str] = None, token_id: Optional[str] = None):
        """
        Initialize the WMA Crossover Analyzer

        Args:
            client_code: Dhan client code (optional)
            token_id: Dhan token ID (optional)
        """
        self.client_code = client_code
        self.token_id = token_id
        self.tsl = None

        if DHAN_AVAILABLE and client_code and token_id:
            try:
                self.tsl = Tradehull(client_code, token_id)
                print("✓ Dhan API connection established")
            except Exception as e:
                print(f"✗ Failed to connect to Dhan API: {e}")
                self.tsl = None

        # Create output directory
        self.output_dir = "output"
        os.makedirs(self.output_dir, exist_ok=True)

    def generate_sample_data(self, symbol: str = "NIFTY", days: int = 5) -> pd.DataFrame:
        """
        Generate sample 1-minute candle data for testing

        Args:
            symbol: Trading symbol
            days: Number of days of data to generate

        Returns:
            DataFrame with OHLCV data
        """
        # Add warmup days for WMA calculation (need at least 90 candles for WMA 90)
        warmup_days = 10  # Extra days to ensure we have enough historical data for WMA 90
        total_days = days + warmup_days

        print(f"📊 Generating sample data for {symbol} ({total_days} total days: {days} analysis + {warmup_days} warmup)")

        # Generate timestamps for market hours (9:15 AM to 3:30 PM)
        timestamps = []
        base_date = datetime.datetime.now() - datetime.timedelta(days=total_days)

        for day in range(total_days):
            current_date = base_date + datetime.timedelta(days=day)
            # Skip weekends
            if current_date.weekday() >= 5:
                continue

            # Market hours: 9:15 AM to 3:30 PM (375 minutes)
            start_time = current_date.replace(hour=9, minute=15, second=0, microsecond=0)
            for minute in range(375):  # 375 minutes in trading session
                timestamps.append(start_time + datetime.timedelta(minutes=minute))

        # Generate realistic price data with trend and volatility
        np.random.seed(42)  # For reproducible results
        base_price = 24000  # Starting price for NIFTY

        data = []
        current_price = base_price

        for i, timestamp in enumerate(timestamps):
            # Add some trend and random walk
            trend = 0.001 * np.sin(i / 100)  # Slight trending component
            volatility = np.random.normal(0, 0.002)  # Random volatility

            price_change = trend + volatility
            current_price = current_price * (1 + price_change)

            # Generate OHLC data
            high = current_price * (1 + abs(np.random.normal(0, 0.001)))
            low = current_price * (1 - abs(np.random.normal(0, 0.001)))
            open_price = current_price + np.random.normal(0, current_price * 0.0005)
            close_price = current_price
            volume = np.random.randint(1000, 10000)

            data.append({
                'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S+05:30'),
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })

        df = pd.DataFrame(data)

        # Mark warmup vs analysis data
        df = self._mark_analysis_and_warmup_data(df, days)

        print(f"✓ Generated {len(df)} total data points")
        warmup_count = len(df[df['is_warmup'] == True])
        analysis_count = len(df[df['is_warmup'] == False])
        print(f"📈 Warmup data: {warmup_count} points")
        print(f"🎯 Analysis data: {analysis_count} points")

        return df

    def fetch_live_data(self, symbol: str = "NIFTY", exchange: str = "INDEX", days: int = 2) -> Optional[pd.DataFrame]:
        """
        Fetch live 1-minute candle data from Dhan API for specified days with EMA warmup data

        Args:
            symbol: Trading symbol
            exchange: Exchange name
            days: Number of trading days to fetch for analysis

        Returns:
            DataFrame with OHLCV data including warmup data or None if failed
        """
        if not self.tsl:
            print("✗ Dhan API not available")
            return None

        try:
            # Calculate total days needed: analysis days + warmup days for WMA calculation
            # We need at least 90 previous candles for WMA90 warmup, so fetch extra days
            # Assuming ~375 candles per trading day, we need at least 1 extra day for 90 candles
            warmup_days = 10  # Extra days to ensure we have enough historical data for WMA 90
            total_days_needed = days + warmup_days

            print(f"📡 Fetching {total_days_needed} days of data ({days} analysis + {warmup_days} warmup) for {symbol} from {exchange}")

            # Fetch historical data from Dhan API (automatically gets last 365 days)
            data = self.tsl.get_historical_data(
                tradingsymbol=symbol,
                exchange=exchange,
                timeframe="1"  # 1-minute timeframe
            )

            if data is None or len(data) == 0:
                print("✗ No data received from Dhan API")
                return None

            # Convert to DataFrame if it's not already
            if not isinstance(data, pd.DataFrame):
                df = pd.DataFrame(data)
            else:
                df = data.copy()

            # Filter for market hours only (9:15 AM to 3:30 PM IST)
            df = self._filter_market_hours(df)

            # Get data for analysis days + warmup days
            df_with_warmup = self._keep_last_trading_days(df, total_days_needed)

            # Mark which data is for analysis vs warmup
            df_with_warmup = self._mark_analysis_and_warmup_data(df_with_warmup, days)

            print(f"✓ Fetched {len(df_with_warmup)} total data points from Dhan API")
            print(f"📊 Full data range: {df_with_warmup['timestamp'].iloc[0]} to {df_with_warmup['timestamp'].iloc[-1]}")

            # Show breakdown of warmup vs analysis data
            warmup_count = len(df_with_warmup[df_with_warmup['is_warmup'] == True])
            analysis_count = len(df_with_warmup[df_with_warmup['is_warmup'] == False])
            print(f"📈 Warmup data: {warmup_count} points")
            print(f"🎯 Analysis data: {analysis_count} points")

            return df_with_warmup

        except Exception as e:
            print(f"✗ Failed to fetch data from Dhan API: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _filter_market_hours(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Filter data to include only market hours (9:15 AM to 3:30 PM IST)

        Args:
            data: DataFrame with timestamp column

        Returns:
            Filtered DataFrame
        """
        df = data.copy()

        # Convert timestamp to datetime if it's not already
        if 'timestamp' in df.columns:
            df['timestamp_dt'] = pd.to_datetime(df['timestamp'])
        else:
            # Try common timestamp column names
            timestamp_cols = ['time', 'datetime', 'date']
            for col in timestamp_cols:
                if col in df.columns:
                    df['timestamp'] = df[col]
                    df['timestamp_dt'] = pd.to_datetime(df[col])
                    break

        # Filter for market hours (9:15 AM to 3:30 PM)
        market_start = 9 * 60 + 15  # 9:15 AM in minutes
        market_end = 15 * 60 + 30   # 3:30 PM in minutes

        df['hour_minute'] = df['timestamp_dt'].dt.hour * 60 + df['timestamp_dt'].dt.minute

        # Filter for market hours and weekdays only
        df_filtered = df[
            (df['hour_minute'] >= market_start) &
            (df['hour_minute'] <= market_end) &
            (df['timestamp_dt'].dt.weekday < 5)  # Monday=0, Friday=4
        ].copy()

        # Clean up temporary columns
        df_filtered = df_filtered.drop(['timestamp_dt', 'hour_minute'], axis=1)

        return df_filtered

    def _keep_last_trading_days(self, data: pd.DataFrame, days: int) -> pd.DataFrame:
        """
        Keep only the last N trading days of data

        Args:
            data: DataFrame with timestamp column
            days: Number of trading days to keep

        Returns:
            DataFrame with last N trading days
        """
        df = data.copy()

        # Convert timestamp to datetime
        df['timestamp_dt'] = pd.to_datetime(df['timestamp'])
        df['date'] = df['timestamp_dt'].dt.date

        # Get unique trading dates and sort them
        unique_dates = sorted(df['date'].unique(), reverse=True)

        # Take the last 'days' trading days
        last_days = unique_dates[:days]

        # Filter data for these days
        df_filtered = df[df['date'].isin(last_days)].copy()

        # Sort by timestamp
        df_filtered = df_filtered.sort_values('timestamp_dt')

        # Clean up temporary columns
        df_filtered = df_filtered.drop(['timestamp_dt', 'date'], axis=1)

        print(f"📅 Keeping data for {len(last_days)} trading days: {last_days}")

        return df_filtered

    def _mark_analysis_and_warmup_data(self, data: pd.DataFrame, analysis_days: int) -> pd.DataFrame:
        """
        Mark which data points are for analysis vs warmup

        Args:
            data: DataFrame with timestamp column
            analysis_days: Number of days for actual analysis

        Returns:
            DataFrame with is_warmup column added
        """
        df = data.copy()

        # Convert timestamp to datetime
        df['timestamp_dt'] = pd.to_datetime(df['timestamp'])
        df['date'] = df['timestamp_dt'].dt.date

        # Get unique trading dates and sort them
        unique_dates = sorted(df['date'].unique(), reverse=True)

        # The last 'analysis_days' are for analysis, rest are warmup
        analysis_dates = unique_dates[:analysis_days]

        # Mark warmup vs analysis data
        df['is_warmup'] = ~df['date'].isin(analysis_dates)

        # Clean up temporary columns
        df = df.drop(['timestamp_dt', 'date'], axis=1)

        return df

    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate WMA indicators for the given data using warmup data for proper initialization

        Args:
            data: DataFrame with OHLCV data including warmup data

        Returns:
            DataFrame with WMA columns added (only analysis data returned)
        """
        print("📈 Calculating WMA indicators with proper warmup initialization")

        # Make a copy to avoid modifying original data
        df = data.copy()

        # Calculate WMAs manually for ALL data (including warmup)
        print("   📊 Calculating WMA 5, WMA 10, WMA 65, and WMA 90...")
        df['wma_5'] = self._calculate_wma_manual(df['close'], 5)
        df['wma_10'] = self._calculate_wma_manual(df['close'], 10)
        df['wma_65'] = self._calculate_wma_manual(df['close'], 65)
        df['wma_90'] = self._calculate_wma_manual(df['close'], 90)

        # Calculate differences for crossover detection
        df['wma_5_10_diff'] = df['wma_5'] - df['wma_10']
        df['wma_10_65_diff'] = df['wma_10'] - df['wma_65']
        df['wma_5_90_diff'] = df['wma_5'] - df['wma_90']

        # Remove rows with NaN values (first 90 rows won't have WMA 90 values)
        df = df.dropna()

        # Separate warmup and analysis data
        warmup_data = df[df['is_warmup'] == True]
        analysis_data = df[df['is_warmup'] == False].copy()

        print(f"✓ WMA indicators calculated using {len(warmup_data)} warmup points")
        print(f"📊 Analysis data: {len(analysis_data)} points with properly initialized WMA indicators")

        # Validate that we have proper indicator initialization
        if len(warmup_data) > 0 and len(analysis_data) > 0:
            first_analysis_point = analysis_data.iloc[0]
            print(f"🎯 First analysis point WMA values:")
            print(f"   📅 Time: {first_analysis_point['timestamp']}")
            print(f"   💰 Close: {first_analysis_point['close']:.2f}")
            print(f"   📊 WMA5: {first_analysis_point['wma_5']:.2f}, WMA10: {first_analysis_point['wma_10']:.2f}")
            print(f"   📉 WMA65: {first_analysis_point['wma_65']:.2f}, WMA90: {first_analysis_point['wma_90']:.2f}")

        # Return only the analysis data (without warmup)
        return analysis_data



    def _calculate_wma_manual(self, prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Weighted Moving Average manually

        Args:
            prices: Series of prices
            period: WMA period

        Returns:
            Series with WMA values
        """
        # Initialize WMA series
        wma = pd.Series(index=prices.index, dtype=float)

        # Calculate weights (1, 2, 3, ..., period)
        weights = np.arange(1, period + 1)
        weight_sum = weights.sum()

        # Calculate WMA for each position
        for i in range(len(prices)):
            if i < period - 1:
                # Not enough data points, set as NaN
                wma.iloc[i] = np.nan
            else:
                # Get the last 'period' prices
                price_window = prices.iloc[i - period + 1:i + 1]

                # Calculate weighted average
                weighted_sum = (price_window * weights).sum()
                wma.iloc[i] = weighted_sum / weight_sum

        return wma

    def detect_crossovers(self, data: pd.DataFrame, min_separation_minutes: int = 5,
                         confirmation_periods: int = 2) -> pd.DataFrame:
        """
        Detect WMA crossovers with improved accuracy and noise reduction

        Args:
            data: DataFrame with WMA data
            min_separation_minutes: Minimum minutes between signals
            confirmation_periods: Number of periods to confirm crossover

        Returns:
            DataFrame with crossover signals
        """
        df = data.copy()

        print(f"🔍 Detecting WMA crossovers with improved accuracy")

        # Initialize WMA signal columns
        df['wma_5_10_buy_signal'] = False
        df['wma_5_10_sell_signal'] = False
        df['wma_5_10_signal'] = 'HOLD'
        df['raw_wma_5_10_buy_signal'] = False
        df['raw_wma_5_10_sell_signal'] = False

        df['wma_10_65_buy_signal'] = False
        df['wma_10_65_sell_signal'] = False
        df['wma_10_65_signal'] = 'HOLD'
        df['raw_wma_10_65_buy_signal'] = False
        df['raw_wma_10_65_sell_signal'] = False

        df['wma_5_90_buy_signal'] = False
        df['wma_5_90_sell_signal'] = False
        df['wma_5_90_signal'] = 'HOLD'
        df['raw_wma_5_90_buy_signal'] = False
        df['raw_wma_5_90_sell_signal'] = False

        # Detect crossovers with confirmation
        for i in range(confirmation_periods, len(df)):

            # WMA 5/10 crossovers
            current_wma_5_10_diff = df.iloc[i]['wma_5_10_diff']
            prev_wma_5_10_diff = df.iloc[i-1]['wma_5_10_diff']
            prev2_wma_5_10_diff = df.iloc[i-2]['wma_5_10_diff'] if i >= 2 else prev_wma_5_10_diff

            # WMA 5 crosses above WMA 10
            if (current_wma_5_10_diff > 0 and prev_wma_5_10_diff > 0 and prev2_wma_5_10_diff <= 0):
                df.iloc[i, df.columns.get_loc('raw_wma_5_10_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_10_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_10_signal')] = 'BUY'

            # WMA 5 crosses below WMA 10
            elif (current_wma_5_10_diff < 0 and prev_wma_5_10_diff < 0 and prev2_wma_5_10_diff >= 0):
                df.iloc[i, df.columns.get_loc('raw_wma_5_10_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_10_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_10_signal')] = 'SELL'

            # WMA 10/65 crossovers
            current_wma_10_65_diff = df.iloc[i]['wma_10_65_diff']
            prev_wma_10_65_diff = df.iloc[i-1]['wma_10_65_diff']
            prev2_wma_10_65_diff = df.iloc[i-2]['wma_10_65_diff'] if i >= 2 else prev_wma_10_65_diff

            # WMA 10 crosses above WMA 65
            if (current_wma_10_65_diff > 0 and prev_wma_10_65_diff > 0 and prev2_wma_10_65_diff <= 0):
                df.iloc[i, df.columns.get_loc('raw_wma_10_65_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_10_65_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_10_65_signal')] = 'BUY'

            # WMA 10 crosses below WMA 65
            elif (current_wma_10_65_diff < 0 and prev_wma_10_65_diff < 0 and prev2_wma_10_65_diff >= 0):
                df.iloc[i, df.columns.get_loc('raw_wma_10_65_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_10_65_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_10_65_signal')] = 'SELL'

            # WMA 5/90 crossovers
            current_wma_5_90_diff = df.iloc[i]['wma_5_90_diff']
            prev_wma_5_90_diff = df.iloc[i-1]['wma_5_90_diff']
            prev2_wma_5_90_diff = df.iloc[i-2]['wma_5_90_diff'] if i >= 2 else prev_wma_5_90_diff

            # WMA 5 crosses above WMA 90
            if (current_wma_5_90_diff > 0 and prev_wma_5_90_diff > 0 and prev2_wma_5_90_diff <= 0):
                df.iloc[i, df.columns.get_loc('raw_wma_5_90_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_90_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_90_signal')] = 'BUY'

            # WMA 5 crosses below WMA 90
            elif (current_wma_5_90_diff < 0 and prev_wma_5_90_diff < 0 and prev2_wma_5_90_diff >= 0):
                df.iloc[i, df.columns.get_loc('raw_wma_5_90_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_90_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('wma_5_90_signal')] = 'SELL'

        # Store raw signals count before filtering
        raw_wma_5_10_buy = df['raw_wma_5_10_buy_signal'].sum()
        raw_wma_5_10_sell = df['raw_wma_5_10_sell_signal'].sum()
        raw_wma_10_65_buy = df['raw_wma_10_65_buy_signal'].sum()
        raw_wma_10_65_sell = df['raw_wma_10_65_sell_signal'].sum()
        raw_wma_5_90_buy = df['raw_wma_5_90_buy_signal'].sum()
        raw_wma_5_90_sell = df['raw_wma_5_90_sell_signal'].sum()

        # Apply minimum separation filter for each signal type
        df = self._apply_wma_signal_separation(df, min_separation_minutes)

        # Create comprehensive signal columns that show all crossovers
        df['all_wma_5_10_crossovers'] = 'HOLD'
        df.loc[df['raw_wma_5_10_buy_signal'], 'all_wma_5_10_crossovers'] = 'BUY'
        df.loc[df['raw_wma_5_10_sell_signal'], 'all_wma_5_10_crossovers'] = 'SELL'

        df['all_wma_10_65_crossovers'] = 'HOLD'
        df.loc[df['raw_wma_10_65_buy_signal'], 'all_wma_10_65_crossovers'] = 'BUY'
        df.loc[df['raw_wma_10_65_sell_signal'], 'all_wma_10_65_crossovers'] = 'SELL'

        df['all_wma_5_90_crossovers'] = 'HOLD'
        df.loc[df['raw_wma_5_90_buy_signal'], 'all_wma_5_90_crossovers'] = 'BUY'
        df.loc[df['raw_wma_5_90_sell_signal'], 'all_wma_5_90_crossovers'] = 'SELL'

        # Count final signals after filtering
        wma_5_10_buy_count = df['wma_5_10_buy_signal'].sum()
        wma_5_10_sell_count = df['wma_5_10_sell_signal'].sum()
        wma_10_65_buy_count = df['wma_10_65_buy_signal'].sum()
        wma_10_65_sell_count = df['wma_10_65_sell_signal'].sum()
        wma_5_90_buy_count = df['wma_5_90_buy_signal'].sum()
        wma_5_90_sell_count = df['wma_5_90_sell_signal'].sum()

        print(f"✓ WMA crossover detection completed:")
        print(f"  📊 WMA 5/10: {wma_5_10_buy_count} BUY, {wma_5_10_sell_count} SELL (Raw: {raw_wma_5_10_buy}/{raw_wma_5_10_sell})")
        print(f"  📉 WMA 10/65: {wma_10_65_buy_count} BUY, {wma_10_65_sell_count} SELL (Raw: {raw_wma_10_65_buy}/{raw_wma_10_65_sell})")
        print(f"  🎯 WMA 5/90: {wma_5_90_buy_count} BUY, {wma_5_90_sell_count} SELL (Raw: {raw_wma_5_90_buy}/{raw_wma_5_90_sell})")
        print(f"  ⏱️  Min separation: {min_separation_minutes} minutes")
        print(f"  ✅ Confirmation periods: {confirmation_periods}")

        return df

    def _apply_signal_separation(self, data: pd.DataFrame, min_minutes: int) -> pd.DataFrame:
        """
        Apply minimum separation between signals to avoid noise

        Args:
            data: DataFrame with signals
            min_minutes: Minimum minutes between signals

        Returns:
            DataFrame with filtered signals
        """
        df = data.copy()

        # Convert timestamp to datetime for calculation
        df['timestamp_dt'] = pd.to_datetime(df['timestamp'])

        # Track last signal time
        last_signal_time = None

        for i in range(len(df)):
            if df.iloc[i]['signal'] in ['BUY', 'SELL']:
                current_time = df.iloc[i]['timestamp_dt']

                # If this is not the first signal, check separation
                if last_signal_time is not None:
                    time_diff = (current_time - last_signal_time).total_seconds() / 60

                    # If too close to previous signal, remove this signal
                    if time_diff < min_minutes:
                        df.iloc[i, df.columns.get_loc('buy_signal')] = False
                        df.iloc[i, df.columns.get_loc('sell_signal')] = False
                        df.iloc[i, df.columns.get_loc('signal')] = 'HOLD'
                        continue

                # Update last signal time
                last_signal_time = current_time

        # Drop the temporary datetime column
        df = df.drop('timestamp_dt', axis=1)

        return df

    def _apply_wma_signal_separation(self, data: pd.DataFrame, min_minutes: int) -> pd.DataFrame:
        """
        Apply minimum separation between WMA signals to avoid noise

        Args:
            data: DataFrame with WMA signals
            min_minutes: Minimum minutes between signals

        Returns:
            DataFrame with filtered WMA signals
        """
        df = data.copy()

        # Convert timestamp to datetime for calculation
        df['timestamp_dt'] = pd.to_datetime(df['timestamp'])

        # Track last signal time for each signal type
        last_wma_5_10_signal_time = None
        last_wma_10_65_signal_time = None
        last_wma_5_90_signal_time = None

        for i in range(len(df)):
            current_time = df.iloc[i]['timestamp_dt']

            # WMA 5/10 signals
            if df.iloc[i]['wma_5_10_signal'] in ['BUY', 'SELL']:
                if last_wma_5_10_signal_time is not None:
                    time_diff = (current_time - last_wma_5_10_signal_time).total_seconds() / 60
                    if time_diff < min_minutes:
                        df.iloc[i, df.columns.get_loc('wma_5_10_buy_signal')] = False
                        df.iloc[i, df.columns.get_loc('wma_5_10_sell_signal')] = False
                        df.iloc[i, df.columns.get_loc('wma_5_10_signal')] = 'HOLD'
                        continue
                last_wma_5_10_signal_time = current_time

            # WMA 10/65 signals
            if df.iloc[i]['wma_10_65_signal'] in ['BUY', 'SELL']:
                if last_wma_10_65_signal_time is not None:
                    time_diff = (current_time - last_wma_10_65_signal_time).total_seconds() / 60
                    if time_diff < min_minutes:
                        df.iloc[i, df.columns.get_loc('wma_10_65_buy_signal')] = False
                        df.iloc[i, df.columns.get_loc('wma_10_65_sell_signal')] = False
                        df.iloc[i, df.columns.get_loc('wma_10_65_signal')] = 'HOLD'
                        continue
                last_wma_10_65_signal_time = current_time

            # WMA 5/90 signals
            if df.iloc[i]['wma_5_90_signal'] in ['BUY', 'SELL']:
                if last_wma_5_90_signal_time is not None:
                    time_diff = (current_time - last_wma_5_90_signal_time).total_seconds() / 60
                    if time_diff < min_minutes:
                        df.iloc[i, df.columns.get_loc('wma_5_90_buy_signal')] = False
                        df.iloc[i, df.columns.get_loc('wma_5_90_sell_signal')] = False
                        df.iloc[i, df.columns.get_loc('wma_5_90_signal')] = 'HOLD'
                        continue
                last_wma_5_90_signal_time = current_time

        # Drop the temporary datetime column
        df = df.drop('timestamp_dt', axis=1)

        return df

    def save_candle_data(self, data: pd.DataFrame, filename: str = "candle_data.csv") -> str:
        """
        Save all 1-minute candle data with EMA values and crossover signals to CSV

        Args:
            data: DataFrame with candle data, EMA values, and signals
            filename: Output filename

        Returns:
            Full path to saved file
        """
        filepath = os.path.join(self.output_dir, filename)

        # Select relevant columns for enhanced candle data with WMA indicators only
        candle_df = data[[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'wma_5', 'wma_10', 'wma_65', 'wma_90',
            'all_wma_5_10_crossovers', 'all_wma_10_65_crossovers', 'all_wma_5_90_crossovers',
            'wma_5_10_signal', 'wma_10_65_signal', 'wma_5_90_signal'
        ]].copy()

        # Round all indicator values for better readability
        candle_df['wma_5'] = candle_df['wma_5'].round(2)
        candle_df['wma_10'] = candle_df['wma_10'].round(2)
        candle_df['wma_65'] = candle_df['wma_65'].round(2)
        candle_df['wma_90'] = candle_df['wma_90'].round(2)

        # Add differences for analysis
        candle_df['wma_5_10_diff'] = (candle_df['wma_5'] - candle_df['wma_10']).round(2)
        candle_df['wma_10_65_diff'] = (candle_df['wma_10'] - candle_df['wma_65']).round(2)
        candle_df['wma_5_90_diff'] = (candle_df['wma_5'] - candle_df['wma_90']).round(2)

        # Add crossover type indicators
        candle_df['wma_5_10_crossover_type'] = candle_df['all_wma_5_10_crossovers'].apply(
            lambda x: 'Bullish' if x == 'BUY' else ('Bearish' if x == 'SELL' else 'None')
        )
        candle_df['wma_10_65_crossover_type'] = candle_df['all_wma_10_65_crossovers'].apply(
            lambda x: 'Bullish' if x == 'BUY' else ('Bearish' if x == 'SELL' else 'None')
        )
        candle_df['wma_5_90_crossover_type'] = candle_df['all_wma_5_90_crossovers'].apply(
            lambda x: 'Bullish' if x == 'BUY' else ('Bearish' if x == 'SELL' else 'None')
        )

        # Add filtered signal indicators
        candle_df['wma_5_10_filtered_signal'] = candle_df['wma_5_10_signal'].apply(
            lambda x: 'Yes' if x in ['BUY', 'SELL'] else 'No'
        )
        candle_df['wma_10_65_filtered_signal'] = candle_df['wma_10_65_signal'].apply(
            lambda x: 'Yes' if x in ['BUY', 'SELL'] else 'No'
        )
        candle_df['wma_5_90_filtered_signal'] = candle_df['wma_5_90_signal'].apply(
            lambda x: 'Yes' if x in ['BUY', 'SELL'] else 'No'
        )

        # Reorder columns for better readability
        candle_df = candle_df[[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            # WMA indicators
            'wma_5', 'wma_10', 'wma_65', 'wma_90',
            'wma_5_10_diff', 'all_wma_5_10_crossovers', 'wma_5_10_crossover_type', 'wma_5_10_filtered_signal',
            'wma_10_65_diff', 'all_wma_10_65_crossovers', 'wma_10_65_crossover_type', 'wma_10_65_filtered_signal',
            'wma_5_90_diff', 'all_wma_5_90_crossovers', 'wma_5_90_crossover_type', 'wma_5_90_filtered_signal'
        ]]

        # Save to CSV
        candle_df.to_csv(filepath, index=False)

        # Count crossovers in the data
        wma_5_10_crossovers = len(candle_df[candle_df['all_wma_5_10_crossovers'] != 'HOLD'])
        wma_10_65_crossovers = len(candle_df[candle_df['all_wma_10_65_crossovers'] != 'HOLD'])
        wma_5_90_crossovers = len(candle_df[candle_df['all_wma_5_90_crossovers'] != 'HOLD'])

        wma_5_10_filtered = len(candle_df[candle_df['wma_5_10_filtered_signal'] == 'Yes'])
        wma_10_65_filtered = len(candle_df[candle_df['wma_10_65_filtered_signal'] == 'Yes'])
        wma_5_90_filtered = len(candle_df[candle_df['wma_5_90_filtered_signal'] == 'Yes'])

        print(f"💾 Enhanced candle data with WMA indicators saved to: {filepath}")
        print(f"   📊 Total records: {len(candle_df)}")
        print(f"   📊 WMA indicators: WMA 5, WMA 10, WMA 65, WMA 90 + crossovers")
        print(f"   🎯 Crossover occurrences:")
        print(f"      WMA 5/10: {wma_5_10_crossovers} | WMA 10/65: {wma_10_65_crossovers} | WMA 5/90: {wma_5_90_crossovers}")
        print(f"   ✅ Filtered signals (after separation):")
        print(f"      WMA 5/10: {wma_5_10_filtered} | WMA 10/65: {wma_10_65_filtered} | WMA 5/90: {wma_5_90_filtered}")
        print(f"   📊 Columns: OHLCV + WMA indicators + Crossovers + Filter_Status")

        return filepath

    def save_signals_data(self, data: pd.DataFrame, filename: str = "signals.csv") -> str:
        """
        Save buy and sell signal data to CSV for all indicator types

        Args:
            data: DataFrame with signal data
            filename: Output filename

        Returns:
            Full path to saved file
        """
        filepath = os.path.join(self.output_dir, filename)

        # Collect all signals from different WMA indicators
        all_signals = []

        # WMA 5/10 signals
        wma_5_10_signals = data[data['wma_5_10_signal'].isin(['BUY', 'SELL'])].copy()
        for _, row in wma_5_10_signals.iterrows():
            all_signals.append({
                'timestamp': row['timestamp'],
                'indicator_type': 'WMA_5_10',
                'signal': row['wma_5_10_signal'],
                'price': row['close'],
                'indicator_1_value': row['wma_5'],
                'indicator_2_value': row['wma_10'],
                'indicator_1_name': 'WMA_5',
                'indicator_2_name': 'WMA_10'
            })

        # WMA 10/65 signals
        wma_10_65_signals = data[data['wma_10_65_signal'].isin(['BUY', 'SELL'])].copy()
        for _, row in wma_10_65_signals.iterrows():
            all_signals.append({
                'timestamp': row['timestamp'],
                'indicator_type': 'WMA_10_65',
                'signal': row['wma_10_65_signal'],
                'price': row['close'],
                'indicator_1_value': row['wma_10'],
                'indicator_2_value': row['wma_65'],
                'indicator_1_name': 'WMA_10',
                'indicator_2_name': 'WMA_65'
            })

        # WMA 5/90 signals
        wma_5_90_signals = data[data['wma_5_90_signal'].isin(['BUY', 'SELL'])].copy()
        for _, row in wma_5_90_signals.iterrows():
            all_signals.append({
                'timestamp': row['timestamp'],
                'indicator_type': 'WMA_5_90',
                'signal': row['wma_5_90_signal'],
                'price': row['close'],
                'indicator_1_value': row['wma_5'],
                'indicator_2_value': row['wma_90'],
                'indicator_1_name': 'WMA_5',
                'indicator_2_name': 'WMA_90'
            })

        # Create DataFrame from all signals
        signals_df = pd.DataFrame(all_signals)

        if len(signals_df) > 0:
            # Round indicator values
            signals_df['indicator_1_value'] = signals_df['indicator_1_value'].round(2)
            signals_df['indicator_2_value'] = signals_df['indicator_2_value'].round(2)
            signals_df['price'] = signals_df['price'].round(2)

            # Sort by timestamp
            signals_df = signals_df.sort_values('timestamp')

            # Reorder columns
            signals_df = signals_df[[
                'timestamp', 'indicator_type', 'signal', 'price',
                'indicator_1_name', 'indicator_1_value',
                'indicator_2_name', 'indicator_2_value'
            ]]

        # Save to CSV
        signals_df.to_csv(filepath, index=False)

        print(f"💾 All WMA signals data saved to: {filepath}")
        print(f"   📊 Total signal records: {len(signals_df)}")
        print(f"   📊 WMA 5/10 signals: {len(wma_5_10_signals)}")
        print(f"   📉 WMA 10/65 signals: {len(wma_10_65_signals)}")
        print(f"   🎯 WMA 5/90 signals: {len(wma_5_90_signals)}")

        return filepath

    def validate_signals(self, data: pd.DataFrame) -> None:
        """
        Validate and display WMA signal details for debugging

        Args:
            data: DataFrame with WMA signals
        """
        print(f"\n🔍 WMA SIGNAL VALIDATION")
        print("-" * 80)

        # Validate WMA 5/10 signals
        wma_5_10_signals = data[data['wma_5_10_signal'].isin(['BUY', 'SELL'])].copy()
        if len(wma_5_10_signals) > 0:
            print(f"\n📊 WMA 5/10 SIGNALS ({len(wma_5_10_signals)} signals)")
            for _, row in wma_5_10_signals.iterrows():
                signal_type = row['wma_5_10_signal']
                wma5 = row['wma_5']
                wma10 = row['wma_10']
                diff = wma5 - wma10

                # Validate signal logic
                if signal_type == 'BUY' and diff <= 0:
                    status = "❌ INVALID"
                elif signal_type == 'SELL' and diff >= 0:
                    status = "❌ INVALID"
                else:
                    status = "✅ VALID"

                print(f"{status} {row['timestamp']}: {signal_type} at {row['close']:.2f}")
                print(f"      WMA5: {wma5:.2f}, WMA10: {wma10:.2f}, Diff: {diff:.2f}")

        # Validate WMA 10/65 signals
        wma_10_65_signals = data[data['wma_10_65_signal'].isin(['BUY', 'SELL'])].copy()
        if len(wma_10_65_signals) > 0:
            print(f"\n📉 WMA 10/65 SIGNALS ({len(wma_10_65_signals)} signals)")
            for _, row in wma_10_65_signals.iterrows():
                signal_type = row['wma_10_65_signal']
                wma10 = row['wma_10']
                wma65 = row['wma_65']
                diff = wma10 - wma65

                # Validate signal logic
                if signal_type == 'BUY' and diff <= 0:
                    status = "❌ INVALID"
                elif signal_type == 'SELL' and diff >= 0:
                    status = "❌ INVALID"
                else:
                    status = "✅ VALID"

                print(f"{status} {row['timestamp']}: {signal_type} at {row['close']:.2f}")
                print(f"      WMA10: {wma10:.2f}, WMA65: {wma65:.2f}, Diff: {diff:.2f}")

        # Validate WMA 5/90 signals
        wma_5_90_signals = data[data['wma_5_90_signal'].isin(['BUY', 'SELL'])].copy()
        if len(wma_5_90_signals) > 0:
            print(f"\n🎯 WMA 5/90 SIGNALS ({len(wma_5_90_signals)} signals)")
            for _, row in wma_5_90_signals.iterrows():
                signal_type = row['wma_5_90_signal']
                wma5 = row['wma_5']
                wma90 = row['wma_90']
                diff = wma5 - wma90

                # Validate signal logic
                if signal_type == 'BUY' and diff <= 0:
                    status = "❌ INVALID"
                elif signal_type == 'SELL' and diff >= 0:
                    status = "❌ INVALID"
                else:
                    status = "✅ VALID"

                print(f"{status} {row['timestamp']}: {signal_type} at {row['close']:.2f}")
                print(f"      WMA5: {wma5:.2f}, WMA90: {wma90:.2f}, Diff: {diff:.2f}")

        # Summary
        total_signals = len(wma_5_10_signals) + len(wma_10_65_signals) + len(wma_5_90_signals)
        if total_signals == 0:
            print("⚠️  No WMA signals found to validate")

    def run_analysis(self, symbol: str = "NIFTY", exchange: str = "INDEX",
                    use_sample_data: bool = False, days: int = 5,
                    min_separation: int = 5, confirmation_periods: int = 2) -> Tuple[str, str]:
        """
        Run complete WMA crossover analysis

        Args:
            symbol: Trading symbol to analyze
            exchange: Exchange name
            use_sample_data: Whether to use sample data instead of live data
            days: Number of days for sample data generation
            min_separation: Minimum minutes between signals
            confirmation_periods: Number of periods to confirm crossover

        Returns:
            Tuple of (candle_data_file_path, signals_file_path)
        """
        print("🚀 Starting WMA Crossover Analysis")
        print("=" * 50)

        # Step 1: Get data
        if use_sample_data:
            print("🧪 Using sample data as requested")
            data = self.generate_sample_data(symbol, days)
        elif not self.tsl:
            print("⚠️  Dhan API not configured, falling back to sample data")
            data = self.generate_sample_data(symbol, days)
        else:
            print("🔌 Attempting to fetch real data from Dhan API")
            data = self.fetch_live_data(symbol, exchange, days)
            if data is None:
                print("⚠️  Failed to fetch real data, falling back to sample data")
                data = self.generate_sample_data(symbol, days)

        # Step 2: Calculate WMA indicators
        data_with_indicators = self.calculate_indicators(data)

        # Step 3: Detect WMA crossovers with improved parameters
        data_with_signals = self.detect_crossovers(
            data_with_indicators,
            min_separation_minutes=min_separation,
            confirmation_periods=confirmation_periods
        )

        # Step 4: Validate WMA signals
        self.validate_signals(data_with_signals)

        # Step 5: Save data to CSV files
        candle_file = self.save_candle_data(data_with_signals)
        signals_file = self.save_signals_data(data_with_signals)

        # Step 6: Display summary
        self.display_summary(data_with_signals)

        print("=" * 50)
        print("✅ WMA Analysis completed successfully!")

        return candle_file, signals_file

    def display_summary(self, data: pd.DataFrame) -> None:
        """
        Display WMA analysis summary

        Args:
            data: DataFrame with complete WMA analysis data
        """
        print("\n📊 WMA ANALYSIS SUMMARY")
        print("-" * 30)

        total_records = len(data)

        # Count signals from WMA indicators
        wma_5_10_buy_signals = data['wma_5_10_buy_signal'].sum()
        wma_5_10_sell_signals = data['wma_5_10_sell_signal'].sum()
        wma_10_65_buy_signals = data['wma_10_65_buy_signal'].sum()
        wma_10_65_sell_signals = data['wma_10_65_sell_signal'].sum()
        wma_5_90_buy_signals = data['wma_5_90_buy_signal'].sum()
        wma_5_90_sell_signals = data['wma_5_90_sell_signal'].sum()

        total_buy_signals = wma_5_10_buy_signals + wma_10_65_buy_signals + wma_5_90_buy_signals
        total_sell_signals = wma_5_10_sell_signals + wma_10_65_sell_signals + wma_5_90_sell_signals
        total_signals = total_buy_signals + total_sell_signals

        print(f"Total data points: {total_records}")
        print(f"Total WMA signals generated: {total_signals}")
        print(f"Signal frequency: {(total_signals / total_records * 100):.2f}%")
        print()
        print("📈 WMA Signal Breakdown:")
        print(f"  WMA 5/10: {wma_5_10_buy_signals} BUY, {wma_5_10_sell_signals} SELL")
        print(f"  WMA 10/65: {wma_10_65_buy_signals} BUY, {wma_10_65_sell_signals} SELL")
        print(f"  WMA 5/90: {wma_5_90_buy_signals} BUY, {wma_5_90_sell_signals} SELL")

        if total_signals > 0:
            print("\n🎯 RECENT WMA 5/10 SIGNALS:")
            recent_wma_5_10_signals = data[data['wma_5_10_signal'].isin(['BUY', 'SELL'])].tail(3)
            for _, row in recent_wma_5_10_signals.iterrows():
                signal_emoji = "📈" if row['wma_5_10_signal'] == 'BUY' else "📉"
                print(f"  {signal_emoji} {row['timestamp']}: {row['wma_5_10_signal']} at {row['close']:.2f}")

            print("\n🎯 RECENT WMA 10/65 SIGNALS:")
            recent_wma_10_65_signals = data[data['wma_10_65_signal'].isin(['BUY', 'SELL'])].tail(3)
            for _, row in recent_wma_10_65_signals.iterrows():
                signal_emoji = "📈" if row['wma_10_65_signal'] == 'BUY' else "📉"
                print(f"  {signal_emoji} {row['timestamp']}: {row['wma_10_65_signal']} at {row['close']:.2f}")

            print("\n🎯 RECENT WMA 5/90 SIGNALS:")
            recent_wma_5_90_signals = data[data['wma_5_90_signal'].isin(['BUY', 'SELL'])].tail(3)
            for _, row in recent_wma_5_90_signals.iterrows():
                signal_emoji = "📈" if row['wma_5_90_signal'] == 'BUY' else "📉"
                print(f"  {signal_emoji} {row['timestamp']}: {row['wma_5_90_signal']} at {row['close']:.2f}")


def main():
    """
    Main function to run the WMA crossover analysis
    """
    print("🎯 WMA Crossover Analysis Tool")
    print("=" * 50)

    # Load configuration
    if CONFIG_AVAILABLE:
        config = get_config()

        # Validate configuration
        is_valid, error_msg = validate_config()
        if not is_valid:
            print(f"❌ Configuration error: {error_msg}")
            return

        # Print configuration
        print_config()

        # Extract configuration values
        CLIENT_CODE = config['dhan_client_code']
        TOKEN_ID = config['dhan_token_id']
        SYMBOL = config['symbol']
        EXCHANGE = config['exchange']
        USE_SAMPLE_DATA = config['use_sample_data']
        TRADING_DAYS = config['trading_days']
        MIN_SEPARATION_MINUTES = config['min_separation_minutes']
        CONFIRMATION_PERIODS = config['confirmation_periods']
    else:
        # Fallback configuration
        print("📋 Using default configuration:")
        CLIENT_CODE = None
        TOKEN_ID = None
        SYMBOL = "NIFTY"
        EXCHANGE = "INDEX"
        USE_SAMPLE_DATA = False
        TRADING_DAYS = 2
        MIN_SEPARATION_MINUTES = 10
        CONFIRMATION_PERIODS = 2

        print(f"   Symbol: {SYMBOL}")
        print(f"   Exchange: {EXCHANGE}")
        print(f"   Trading Days: {TRADING_DAYS}")
        print(f"   Use Sample Data: {USE_SAMPLE_DATA}")
        print(f"   Min Separation: {MIN_SEPARATION_MINUTES} minutes")
        print(f"   Confirmation Periods: {CONFIRMATION_PERIODS}")
        print()

    try:
        # Initialize WMA analyzer
        analyzer = WMACrossoverAnalyzer(CLIENT_CODE, TOKEN_ID)

        # Run WMA analysis with improved parameters
        candle_file, signals_file = analyzer.run_analysis(
            symbol=SYMBOL,
            exchange=EXCHANGE,
            use_sample_data=USE_SAMPLE_DATA,
            days=TRADING_DAYS,
            min_separation=MIN_SEPARATION_MINUTES,
            confirmation_periods=CONFIRMATION_PERIODS
        )

        print(f"\n📁 Output files created:")
        print(f"  📊 Candle data: {candle_file}")
        print(f"  🎯 Signals data: {signals_file}")

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
